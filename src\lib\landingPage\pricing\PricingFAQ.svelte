<script lang="ts">
    import { H2, H3, P1, P2, Accordion } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';

    import type { AccordionItemData } from '$lib/ui/Accordion.svelte';

    let {
        headline = "FAQs",
        subheadline = "Everything you need to know about our SAT prep platform.",
        faqs = [
            {
                question: "Is there a 14-day money back guarantee?",
                answer: "Yes, we have a 14-day money back guarantee. This means that you can try DSAT16 risk-free for a total of 21 days (7-day free trial + 14-day money back guarantee after your trial ends). We know you'll LOVE DSAT16, but if for some reason you decide later that you don't want to be a member anymore, we'll happily cancel your account during that window of time with a full refund."
            },
            {
                question: "Do I need a credit card to sign up for the free trial?",
                answer: "No, you don't need a credit card to sign up. You can try DSAT16 for free for 7 days. After that, if you decide to upgrade to a paid plan, you will need to provide a credit card. However, you can cancel your subscription at any time before your trial ends and you will not be charged."
            },
            {
                question: "Who wrote the questions?",
                answer: "The questions and explanations were written by an AI model trained on real SAT questions. The AI model is able to generate questions that are indistinguishable from real SAT questions. ",
            }
        ]
    } = $props();

    // Transform FAQ data to Accordion format
    const accordionItems: AccordionItemData[] = faqs.map((faq, index) => ({
        id: `faq-${index}`,
        title: faq.question,
        content: faq.answer
    }));


</script>

<SectionWrapper --bg-color="var(--white)" --padding-top="4rem" --padding-bottom="4rem">
    <div class="faq-section">
        <div class="faq-header">
            <H2>{@html headline}</H2>
            <P1>{@html subheadline}</P1>
        </div>
        
        <div class="faq-list">
            <Accordion
                items={accordionItems}
                singleOpen={true}
            />
        </div>
    </div>
</SectionWrapper>

<style>
    .faq-section {
        position: relative;
    }

    .faq-header {
        text-align: center;
        margin-bottom: 3rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        position: relative;
        z-index: 1;
    }

    .faq-header :global(h2) {
        color: var(--pitch-black);
    }

    .faq-list {
        max-width: 800px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }
</style>

<script lang="ts">
    import PricingCard from '$lib/ui/pricing-cards/PricingCard.svelte';
    import { SectionWrapper } from '$lib/landingPage';
	import { P2, P3 } from '$lib/ui';
    
    interface Plan {
        label: string;
        description: string;
        monthlyPrice: string;
        monthlyPriceIfPaidYearly: string;
        checklist: { text: string; subtext?: string }[];
    }

    let { 
        pricingPlans = [
            {
                label: "Pro Plan", 
                description: "Get unlimited access to all features. Study at your own pace.",
                monthlyPrice: "$27/mo",
                monthlyPriceIfPaidYearly: "$16/mo", 
                checklist: [
                    { text: "7-day free trial" },
                    { text: "Weekly added mock tests" },
                    { text: "Detailed performance analytics" },
                    { text: "5000+ practice questions" },
                    { text: "Time-optimized Vocab Learning Tool" },
                    { text: "Gamified Learning Experience to keep you motivated" },
                    { text: "14-day money-back guarantee" }
                ]
            },
        ]
    }: { pricingPlans?: Plan[] } = $props();

    let isAnnual = $state(true);

    function toggleBilling() {
        isAnnual = !isAnnual;
    }
</script>

<SectionWrapper --bg-color="var(--purple)" --padding-top="4rem" --padding-bottom="4rem">
    <div class="billing-toggle">
        <div class="toggle-switch">
            <button 
                class="toggle-button annual" 
                class:active={isAnnual}
                onclick={() => toggleBilling()}
            >
                <P2 isBold={true}>Yearly Discount</P2>
            </button>
            <button 
                class="toggle-button monthly" 
                class:active={!isAnnual}
                onclick={() => toggleBilling()}
            >
                <P2 isBold={true}>Monthly Plan</P2>
            </button>
        </div>
    </div>
    
    <div class="pricing-cards-section">
        <div class="pricing-grid">
            {#each pricingPlans as plan}
                {@const monthlyPrice = parseInt(plan.monthlyPrice.replace(/\D/g, ""))}
                {@const yearlyPrice = parseInt(plan.monthlyPriceIfPaidYearly.replace(/\D/g, ""))}
                <PricingCard
                    --box-shadow-color="var(--pitch-black)"
                    --svg-color="var(--aquamarine)"
                    --label-bg-color="var(--light-sky-blue)"
                    label={plan.label}
                    description={plan.description}
                    price={isAnnual ? plan.monthlyPriceIfPaidYearly : plan.monthlyPrice}
                    moneySaved={isAnnual ? `$${(monthlyPrice - yearlyPrice) * 12}` : undefined }
                    checklist={plan.checklist}
                    isAnnual
                />
            {/each}
        </div>
    </div>
</SectionWrapper>

<style>
    .billing-toggle {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        position: relative;
        z-index: 1;
        margin-bottom: 2rem;
    }

    .toggle-switch {
        display: flex;
        background: var(--white);
        border: 3px solid var(--pitch-black);
        border-radius: 0.75rem;
        overflow: hidden;
        position: relative;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
    }

    .toggle-button {
        padding: 0.75rem 1.5rem;
        background: var(--white);
        border: none;
        cursor: pointer;

        border-right: 1px solid var(--pitch-black);
    }

    .toggle-button:last-child {
        border-right: none;
    }

    .toggle-button.active {
        transform: none;
        font-weight: bold;
    }

    .annual.active {
        background: var(--aquamarine);
    }

    .monthly.active {
        background: var(--rose);
    }

    .toggle-button:hover:not(.active) {
        background: var(--very-light-gray);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .toggle-switch {
            width: 100%;
            max-width: 400px;
        }

        .toggle-button {
            padding: 0.5rem 1rem;
        }
    }

    .pricing-cards-section {
        position: relative;
    }



    .pricing-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        max-width: 1000px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .pricing-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }
</style>

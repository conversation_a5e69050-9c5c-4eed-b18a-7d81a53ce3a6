<!--
	@component Accordion
	Simple accordion component with minimal features.
	- Props:
		- items?: Array<{ id?: string; title: string; content?: string }>
		- singleOpen?: boolean = true (only one panel open at a time)
		- defaultOpen?: Array<number | string> (indices or ids to open by default)
	- Usage:
		<Accordion items={[{ title: 'A', content: '...' }]} />
-->

<script lang="ts">
    import { P1, P2 } from './typography';

    export interface AccordionItemData {
        id?: string;
        title: string;
        content?: string;
    }

    interface Props {
        items?: AccordionItemData[];
        singleOpen?: boolean;
        defaultOpen?: Array<number | string>;
    }

    let { items = [], singleOpen = true, defaultOpen = [] }: Props = $props();

    // Track open panels by id (array for reactivity; Set mutations are not tracked by $state)
    let openIds = $state<string[]>([]);
    const buttonEls = $state<(HTMLButtonElement | null)[]>([]);

    // Helper function to get ID for an item
    function getItemId(item: AccordionItemData, index: number): string {
        return item.id || `acc-${index}`;
    }

    // Action to capture button refs for keyboard navigation
    function refButton(node: HTMLButtonElement, i: number) {
        buttonEls[i] = node;
        return {
            destroy() {
                buttonEls[i] = null;
            }
        };
    }

    let initialized = $state(false);
    $effect(() => {
        if (initialized || items.length === 0) return;
        let targets: string[] = [];
        for (const k of defaultOpen) {
            if (typeof k === 'number') {
                if (k >= 0 && k < items.length) {
                    const id = getItemId(items[k], k);
                    targets.push(id);
                }
            } else {
                targets.push(String(k));
            }
        }
        if (singleOpen && targets.length > 1) targets = targets.slice(0, 1);
        openIds = [...targets];
        initialized = true;
    });

    function isOpenIndex(i: number) {
        if (i < 0 || i >= items.length) return false;
        const id = getItemId(items[i], i);
        return openIds.includes(id);
    }

    function toggleIndex(i: number) {
        if (i < 0 || i >= items.length) return;
        const id = getItemId(items[i], i);
        if (singleOpen) {
            openIds = openIds[0] === id ? [] : [id];
        } else {
            const idx = openIds.indexOf(id);
            if (idx !== -1) {
                openIds = [...openIds.slice(0, idx), ...openIds.slice(idx + 1)];
            } else {
                openIds = [...openIds, id];
            }
        }
    }

    function onKeyDown(i: number, e: KeyboardEvent) {
        const key = e.key;
        let next = i;
        if (key === 'ArrowDown') next = (i + 1) % items.length;
        else if (key === 'ArrowUp') next = (i - 1 + items.length) % items.length;
        else if (key === 'Home') next = 0;
        else if (key === 'End') next = items.length - 1;
        else if (key === 'Enter' || key === ' ') {
            e.preventDefault();
            toggleIndex(i);
            return;
        } else return;
        e.preventDefault();
        buttonEls[next]?.focus();
    }
</script>

<div class="accordion">
    {#each items as item, i (item.id || `acc-${i}`)}
        <div class="acc-item">
            <P1>
                <button
                    use:refButton={i}
                    type="button"
                    id={`acc-btn-${getItemId(item, i)}`}
                    class="acc-trigger"
                    aria-expanded={isOpenIndex(i)}
                    aria-controls={`acc-panel-${getItemId(item, i)}`}
                    onkeydown={(e) => onKeyDown(i, e)}
                    onclick={() => toggleIndex(i)}
                >
                    <span>{item.title}</span>
                    <span class="acc-icon" aria-hidden="true">{isOpenIndex(i) ? '−' : '+'}</span>
                </button>
            </P1>
            <div
                id={`acc-panel-${getItemId(item, i)}`}
                class="acc-panel"
                role="region"
                aria-labelledby={`acc-btn-${getItemId(item, i)}`}
                data-open={isOpenIndex(i) ? 'true' : 'false'}
            >
                <div class="acc-inner">
                    {#if item.content}
                        <P2>{item.content}</P2>
                    {/if}
                </div>
            </div>
        </div>
    {/each}
</div>

<style>
    .accordion {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .acc-trigger {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
        padding: 1rem;
    }

	.acc-item {
		background: var(--light-sky-blue);
        border: 3px solid var(--pitch-black);
        border-radius: 0.5rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        cursor: pointer;
        transition: background 0.1s, box-shadow 0.1s;
	}

    .acc-trigger:focus {
        outline: 1px solid var(--sky-blue);
    }

    .acc-icon {
        font-size: 1.25rem;
        line-height: 1;
    }

    .acc-panel {
        overflow: hidden;
        display: grid;
        grid-template-rows: 0fr;
        transition:
            grid-template-rows 220ms ease,
            opacity 220ms ease;
        background: var(--white);
    }

    .acc-panel[data-open='true'] {
        grid-template-rows: 1fr;
        opacity: 1;
		padding: 1rem;
		border-top: 3px solid var(--pitch-black);
		border-radius: 0 0 0.5rem 0.5rem;
    }
    .acc-inner {
        min-height: 0;
    }
</style>

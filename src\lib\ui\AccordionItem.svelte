<!--
  @component AccordionItem
  Building block for Accordion with snippet-based content API.
  - Props:
    - id: string (unique id used for aria attributes)
    - open: boolean (controlled open state)
    - toggle: () => void (toggle handler from Accordion)
    - header?: Snippet (render the header contents)
    - content?: Snippet (render the panel contents)
    - onKeyDown?: (e: KeyboardEvent) => void (optional keyboard navigation delegate)
  - Usage inside Accordion's Item snippet:
    {#snippet Item({ id, open, toggle, onKeyDown, item })}
      <AccordionItem {id} {open} {toggle} {onKeyDown}
        header={() => (<span>{item.title}</span>)}
        content={() => (<p>{item.content}</p>)}
      />
    {/snippet}
-->

<script lang="ts">
  import { P1 } from './typography';
  import type { Snippet } from 'svelte';

  interface Props {
    id: string;
    open: boolean;
    toggle: () => void;
    header?: Snippet<[]>;
    content?: Snippet<[]>;
    onKeyDown?: (e: KeyboardEvent) => void;
  }

  let { id, open, toggle, header, content, onKeyDown }: Props = $props();

  function handleKeyDown(e: KeyboardEvent) {
    if (onKeyDown) return onKeyDown(e);
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      toggle();
    }
  }
</script>

<div class="acc-item">
  <P1>
    <button
      id={`acc-btn-${id}`}
      type="button"
      class="acc-trigger"
      aria-expanded={open}
      aria-controls={`acc-panel-${id}`}
      onkeydown={handleKeyDown}
      onclick={toggle}
    >
      <span>{@render header ? header() : null}</span>
      <span class="acc-icon" aria-hidden="true">{open ? '−' : '+'}</span>
    </button>
  </P1>

  <div
    id={`acc-panel-${id}`}
    class="acc-panel"
    role="region"
    aria-labelledby={`acc-btn-${id}`}
    data-open={open ? 'true' : 'false'}
  >
    <div class="acc-inner">
      {@render content ? content() : null}
    </div>
  </div>
</div>

<style>
  .acc-item {
    background: var(--light-sky-blue);
    border: 3px solid var(--pitch-black);
    border-radius: 0.5rem;
    box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    cursor: pointer;
    transition: background 0.1s, box-shadow 0.1s;
  }

  .acc-trigger {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    padding: 1rem;
  }

  .acc-trigger:focus {
    outline: 1px solid var(--sky-blue);
  }

  .acc-icon {
    font-size: 1.25rem;
    line-height: 1;
  }

  .acc-panel {
    overflow: hidden;
    display: grid;
    grid-template-rows: 0fr;
    transition: grid-template-rows 220ms ease, opacity 220ms ease;
    background: var(--white);
  }

  .acc-panel[data-open='true'] {
    grid-template-rows: 1fr;
    opacity: 1;
    padding: 1rem;
    border-top: 3px solid var(--pitch-black);
    border-radius: 0 0 0.5rem 0.5rem;
  }

  .acc-inner {
    min-height: 0;
  }
</style>

